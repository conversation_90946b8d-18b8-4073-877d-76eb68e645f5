<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Responsive Sidebar Component</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* CSS Variables for Theme Management */
        :root {
            --sidebar-width: 300px;
            --sidebar-collapsed-width: 48px;
            --transition-duration: 0.25s;
            --transition-easing: cubic-bezier(0.4, 0, 0.2, 1);

            /* Dark Theme Colors */
            --bg-primary: #0f0f0f;
            --bg-secondary: #1a1a1a;
            --bg-hover: #2a2a2a;
            --text-primary: #ffffff;
            --text-secondary: #a0a0a0;
            --text-muted: #666666;
            --border-color: #2a2a2a;
            --accent-color: #4a9eff;
            --shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }

        /* Light Theme Colors */
        [data-theme="light"] {
            --bg-primary: #ffffff;
            --bg-secondary: #f8f9fa;
            --bg-hover: #e9ecef;
            --text-primary: #212529;
            --text-secondary: #6c757d;
            --text-muted: #adb5bd;
            --border-color: #dee2e6;
            --accent-color: #0d6efd;
            --shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        /* Reset and Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            transition: background-color var(--transition-duration) var(--transition-easing),
                        color var(--transition-duration) var(--transition-easing);
            overflow-x: hidden;
        }

        /* Theme Toggle Button */
        .theme-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1001;
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            color: var(--text-primary);
            width: 44px;
            height: 44px;
            border-radius: 8px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            transition: all var(--transition-duration) var(--transition-easing);
            box-shadow: var(--shadow);
        }

        .theme-toggle:hover {
            background: var(--bg-hover);
            transform: translateY(-1px);
        }

        /* Sidebar Styles */
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            width: var(--sidebar-width);
            height: 100vh;
            background: var(--bg-primary);
            border-right: 1px solid var(--border-color);
            display: flex;
            flex-direction: column;
            transition: width var(--transition-duration) var(--transition-easing);
            z-index: 1000;
            overflow: hidden;
        }

        .sidebar.collapsed {
            width: var(--sidebar-collapsed-width);
        }

        /* Sidebar Header */
        .sidebar-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 16px;
            border-bottom: 1px solid var(--border-color);
            min-height: 56px;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
        }

        .logo i {
            font-size: 18px;
            color: var(--text-primary);
            width: 20px;
            text-align: center;
        }

        .logo-text {
            transition: opacity var(--transition-duration) var(--transition-easing);
        }

        .sidebar.collapsed .logo-text {
            opacity: 0;
            width: 0;
            overflow: hidden;
        }

        .sidebar-toggle {
            background: none;
            border: none;
            color: var(--text-secondary);
            cursor: pointer;
            padding: 6px;
            border-radius: 4px;
            transition: all var(--transition-duration) var(--transition-easing);
            display: flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
        }

        .sidebar-toggle:hover {
            background: var(--bg-hover);
            color: var(--text-primary);
        }

        .sidebar.collapsed .sidebar-toggle i {
            transform: rotate(180deg);
        }

        /* Search Container */
        .search-container {
            padding: 12px 16px;
            border-bottom: 1px solid var(--border-color);
        }

        .search-box {
            position: relative;
            display: flex;
            align-items: center;
        }

        .search-box i {
            position: absolute;
            left: 12px;
            color: var(--text-muted);
            font-size: 14px;
            z-index: 1;
        }

        .search-box input {
            width: 100%;
            padding: 10px 12px 10px 32px;
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            color: var(--text-primary);
            font-size: 13px;
            transition: all var(--transition-duration) var(--transition-easing);
        }

        .search-box input:focus {
            outline: none;
            border-color: var(--accent-color);
            box-shadow: 0 0 0 3px rgba(74, 158, 255, 0.1);
        }

        .search-box input::placeholder {
            color: var(--text-muted);
        }

        .sidebar.collapsed .search-container {
            padding: 12px;
            display: flex;
            justify-content: center;
        }

        .sidebar.collapsed .search-box {
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .sidebar.collapsed .search-box input {
            display: none;
        }

        .sidebar.collapsed .search-box i {
            position: static;
            color: var(--text-secondary);
            font-size: 14px;
        }

        /* Navigation Styles */
        .sidebar-nav {
            flex: 1;
            overflow-y: auto;
            padding: 8px 0;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section:last-child {
            margin-bottom: 0;
        }

        .section-title {
            font-size: 11px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.8px;
            color: var(--text-muted);
            padding: 12px 16px 6px 16px;
            margin-bottom: 2px;
            transition: opacity var(--transition-duration) var(--transition-easing);
        }

        .sidebar.collapsed .section-title {
            opacity: 0;
            height: 0;
            padding: 0;
            margin: 0;
            overflow: hidden;
        }

        .nav-list {
            list-style: none;
        }

        .nav-item {
            position: relative;
        }

        .nav-link {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px 16px;
            color: var(--text-secondary);
            text-decoration: none;
            font-size: 13px;
            font-weight: 400;
            transition: all var(--transition-duration) var(--transition-easing);
            border-radius: 0;
            position: relative;
            min-height: 36px;
        }

        .nav-link:hover {
            background: var(--bg-hover);
            color: var(--text-primary);
        }

        .nav-link.active {
            background: var(--bg-hover);
            color: var(--accent-color);
        }

        .nav-link i {
            width: 16px;
            text-align: center;
            font-size: 14px;
            flex-shrink: 0;
        }

        .nav-link span {
            flex: 1;
            transition: opacity var(--transition-duration) var(--transition-easing);
        }

        .sidebar.collapsed .nav-link span {
            opacity: 0;
            width: 0;
            overflow: hidden;
        }

        .sidebar.collapsed .nav-link {
            justify-content: center;
            padding: 12px 0;
            min-height: 44px;
            margin: 1px 8px;
            border-radius: 4px;
        }

        .sidebar.collapsed .nav-link i {
            width: auto;
            font-size: 16px;
        }

        .sidebar.collapsed .nav-link:hover {
            background: var(--bg-hover);
        }

        /* Toggle Icon for Collapsible Items */
        .toggle-icon {
            font-size: 12px !important;
            transition: transform var(--transition-duration) var(--transition-easing);
            margin-left: auto;
        }

        .nav-item.expanded .toggle-icon {
            transform: rotate(180deg);
        }

        .sidebar.collapsed .toggle-icon {
            display: none;
        }

        /* Sub Navigation */
        .sub-nav {
            list-style: none;
            max-height: 0;
            overflow: hidden;
            transition: max-height var(--transition-duration) var(--transition-easing);
            background: rgba(0, 0, 0, 0.1);
        }

        .nav-item.expanded .sub-nav {
            max-height: 300px;
        }

        .sidebar.collapsed .sub-nav {
            display: none;
        }

        .sub-nav-item {
            border-left: 2px solid transparent;
            transition: border-color var(--transition-duration) var(--transition-easing);
        }

        .sub-nav-link {
            display: block;
            padding: 10px 16px 10px 48px;
            color: var(--text-secondary);
            text-decoration: none;
            font-size: 13px;
            font-weight: 400;
            transition: all var(--transition-duration) var(--transition-easing);
        }

        .sub-nav-link:hover {
            background: var(--bg-hover);
            color: var(--text-primary);
        }

        .sub-nav-item:hover {
            border-left-color: var(--accent-color);
        }

        /* Sidebar Footer */
        .sidebar-footer {
            border-top: 1px solid var(--border-color);
            padding: 8px 0;
            margin-top: auto;
        }

        /* Main Content */
        .main-content {
            margin-left: var(--sidebar-width);
            transition: margin-left var(--transition-duration) var(--transition-easing);
            min-height: 100vh;
            padding: 40px;
        }

        .sidebar.collapsed + .main-content {
            margin-left: var(--sidebar-collapsed-width);
        }

        .content-container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .content-container h1 {
            font-size: 32px;
            margin-bottom: 16px;
            color: var(--text-primary);
        }

        .content-container p {
            font-size: 16px;
            line-height: 1.6;
            color: var(--text-secondary);
            margin-bottom: 16px;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform var(--transition-duration) var(--transition-easing);
            }

            .sidebar.mobile-open {
                transform: translateX(0);
            }

            .sidebar.collapsed {
                width: var(--sidebar-width);
                transform: translateX(-100%);
            }

            .sidebar.collapsed.mobile-open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .sidebar.collapsed + .main-content {
                margin-left: 0;
            }

            .theme-toggle {
                top: 16px;
                right: 16px;
                width: 40px;
                height: 40px;
                font-size: 16px;
            }

            .main-content {
                padding: 20px 16px;
            }

            .content-container h1 {
                font-size: 24px;
            }
        }

        @media (max-width: 480px) {
            .sidebar {
                width: 100vw;
            }

            .sidebar.collapsed {
                width: 100vw;
            }

            .main-content {
                padding: 16px 12px;
            }

            .content-container h1 {
                font-size: 20px;
            }
        }

        /* Mobile Overlay */
        .sidebar-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(0, 0, 0, 0.5);
            z-index: 999;
            opacity: 0;
            visibility: hidden;
            transition: all var(--transition-duration) var(--transition-easing);
        }

        .sidebar-overlay.active {
            opacity: 1;
            visibility: visible;
        }

        @media (min-width: 769px) {
            .sidebar-overlay {
                display: none;
            }
        }

        /* Mobile Menu Button */
        .mobile-menu-btn {
            display: none;
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1001;
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            color: var(--text-primary);
            width: 44px;
            height: 44px;
            border-radius: 8px;
            cursor: pointer;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            transition: all var(--transition-duration) var(--transition-easing);
            box-shadow: var(--shadow);
        }

        .mobile-menu-btn:hover {
            background: var(--bg-hover);
        }

        @media (max-width: 768px) {
            .mobile-menu-btn {
                display: flex;
            }
        }

        /* Scrollbar Styling */
        .sidebar-nav::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar-nav::-webkit-scrollbar-track {
            background: transparent;
        }

        .sidebar-nav::-webkit-scrollbar-thumb {
            background: var(--border-color);
            border-radius: 3px;
        }

        .sidebar-nav::-webkit-scrollbar-thumb:hover {
            background: var(--text-muted);
        }
    </style>
</head>
<body>
    <!-- Mobile Menu Button -->
    <button id="mobile-menu-btn" class="mobile-menu-btn" aria-label="Toggle mobile menu">
        <i class="fas fa-bars"></i>
    </button>

    <!-- Theme Toggle Button -->
    <button id="theme-toggle" class="theme-toggle" aria-label="Toggle theme">
        <i class="fas fa-moon"></i>
    </button>

    <!-- Sidebar Overlay for Mobile -->
    <div id="sidebar-overlay" class="sidebar-overlay"></div>

    <!-- Sidebar -->
    <aside id="sidebar" class="sidebar">
        <!-- Sidebar Header -->
        <div class="sidebar-header">
            <div class="logo">
                <i class="fas fa-layer-group"></i>
                <span class="logo-text">Tasks</span>
            </div>
            <button id="sidebar-toggle" class="sidebar-toggle" aria-label="Toggle sidebar">
                <i class="fas fa-chevron-left"></i>
            </button>
        </div>

        <!-- Search Bar -->
        <div class="search-container">
            <div class="search-box">
                <i class="fas fa-search"></i>
                <input type="text" placeholder="Search..." aria-label="Search">
            </div>
        </div>

        <!-- Navigation -->
        <nav class="sidebar-nav">
            <!-- Quick Actions Section -->
            <div class="nav-section">
                <h3 class="section-title">Quick Actions</h3>
                <ul class="nav-list">
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <i class="fas fa-plus"></i>
                            <span>New task</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <i class="fas fa-filter"></i>
                            <span>Filter tasks</span>
                        </a>
                    </li>
                </ul>
            </div>

            <!-- My Tasks Section -->
            <div class="nav-section">
                <h3 class="section-title">My Tasks</h3>
                <ul class="nav-list">
                    <li class="nav-item collapsible">
                        <a href="#" class="nav-link" data-toggle="due-today">
                            <i class="fas fa-clock"></i>
                            <span>Due today</span>
                            <i class="fas fa-chevron-down toggle-icon"></i>
                        </a>
                        <ul class="sub-nav" id="due-today">
                            <li class="sub-nav-item">
                                <a href="#" class="sub-nav-link">Review design mockups</a>
                            </li>
                            <li class="sub-nav-item">
                                <a href="#" class="sub-nav-link">Update documentation</a>
                            </li>
                            <li class="sub-nav-item">
                                <a href="#" class="sub-nav-link">Test new feature</a>
                            </li>
                        </ul>
                    </li>
                    <li class="nav-item collapsible">
                        <a href="#" class="nav-link" data-toggle="in-progress">
                            <i class="fas fa-spinner"></i>
                            <span>In progress</span>
                            <i class="fas fa-chevron-down toggle-icon"></i>
                        </a>
                        <ul class="sub-nav" id="in-progress">
                            <li class="sub-nav-item">
                                <a href="#" class="sub-nav-link">Implement user auth</a>
                            </li>
                            <li class="sub-nav-item">
                                <a href="#" class="sub-nav-link">Database migration</a>
                            </li>
                        </ul>
                    </li>
                    <li class="nav-item collapsible">
                        <a href="#" class="nav-link" data-toggle="completed">
                            <i class="fas fa-check-circle"></i>
                            <span>Completed</span>
                            <i class="fas fa-chevron-down toggle-icon"></i>
                        </a>
                        <ul class="sub-nav" id="completed">
                            <!-- Completed tasks would go here -->
                        </ul>
                    </li>
                </ul>
            </div>

            <!-- Other Section -->
            <div class="nav-section">
                <h3 class="section-title">Other</h3>
                <ul class="nav-list">
                    <li class="nav-item collapsible">
                        <a href="#" class="nav-link" data-toggle="priority-tasks">
                            <i class="fas fa-flag"></i>
                            <span>Priority tasks</span>
                            <i class="fas fa-chevron-down toggle-icon"></i>
                        </a>
                        <ul class="sub-nav" id="priority-tasks">
                            <!-- Priority tasks would go here -->
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <i class="fas fa-archive"></i>
                            <span>Archived</span>
                        </a>
                    </li>
                </ul>
            </div>
        </nav>

        <!-- Sidebar Footer -->
        <div class="sidebar-footer">
            <div class="nav-item">
                <a href="#" class="nav-link">
                    <i class="fas fa-cog"></i>
                    <span>Settings</span>
                </a>
            </div>
            <div class="nav-item">
                <a href="#" class="nav-link">
                    <i class="fas fa-user"></i>
                    <span>Profile</span>
                </a>
            </div>
        </div>
    </aside>

    <!-- Main Content -->
    <main class="main-content">
        <div class="content-container">
            <h1>Main Content Area</h1>
            <p>This is the main content area. The sidebar component is fully functional and responsive.</p>
            <p>Try toggling the sidebar, expanding/collapsing sections, and switching between light and dark themes.</p>
        </div>
    </main>

    <script>
        // DOM Elements
        const sidebar = document.getElementById('sidebar');
        const sidebarToggle = document.getElementById('sidebar-toggle');
        const mobileMenuBtn = document.getElementById('mobile-menu-btn');
        const sidebarOverlay = document.getElementById('sidebar-overlay');
        const themeToggle = document.getElementById('theme-toggle');
        const collapsibleItems = document.querySelectorAll('.nav-link[data-toggle]');

        // Theme Management
        function initTheme() {
            const savedTheme = localStorage.getItem('theme') || 'dark';
            document.documentElement.setAttribute('data-theme', savedTheme);
            updateThemeIcon(savedTheme);
        }

        function toggleTheme() {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

            document.documentElement.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);
            updateThemeIcon(newTheme);
        }

        function updateThemeIcon(theme) {
            const icon = themeToggle.querySelector('i');
            icon.className = theme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
        }

        // Sidebar Toggle Functionality
        function toggleSidebar() {
            sidebar.classList.toggle('collapsed');

            // Close all expanded sections when collapsing sidebar
            if (sidebar.classList.contains('collapsed')) {
                collapsibleItems.forEach(item => {
                    const parentItem = item.closest('.nav-item');
                    parentItem.classList.remove('expanded');
                });
            }
        }

        // Mobile Menu Functionality
        function toggleMobileMenu() {
            sidebar.classList.toggle('mobile-open');
            sidebarOverlay.classList.toggle('active');
            document.body.style.overflow = sidebar.classList.contains('mobile-open') ? 'hidden' : '';
        }

        function closeMobileMenu() {
            sidebar.classList.remove('mobile-open');
            sidebarOverlay.classList.remove('active');
            document.body.style.overflow = '';
        }

        // Collapsible Navigation Items
        function toggleCollapsible(event) {
            event.preventDefault();

            // Don't toggle if sidebar is collapsed on desktop
            if (sidebar.classList.contains('collapsed') && window.innerWidth > 768) {
                return;
            }

            const link = event.currentTarget;
            const parentItem = link.closest('.nav-item');
            const isExpanded = parentItem.classList.contains('expanded');

            // Close all other expanded items in the same section
            const section = parentItem.closest('.nav-section');
            const otherExpandedItems = section.querySelectorAll('.nav-item.expanded');
            otherExpandedItems.forEach(item => {
                if (item !== parentItem) {
                    item.classList.remove('expanded');
                }
            });

            // Toggle current item
            parentItem.classList.toggle('expanded', !isExpanded);
        }

        // Handle Window Resize
        function handleResize() {
            if (window.innerWidth > 768) {
                closeMobileMenu();
            }
        }

        // Event Listeners
        themeToggle.addEventListener('click', toggleTheme);
        sidebarToggle.addEventListener('click', toggleSidebar);
        mobileMenuBtn.addEventListener('click', toggleMobileMenu);
        sidebarOverlay.addEventListener('click', closeMobileMenu);
        window.addEventListener('resize', handleResize);

        // Add event listeners to collapsible items
        collapsibleItems.forEach(item => {
            item.addEventListener('click', toggleCollapsible);
        });

        // Keyboard Navigation
        document.addEventListener('keydown', (event) => {
            // Close mobile menu with Escape key
            if (event.key === 'Escape' && sidebar.classList.contains('mobile-open')) {
                closeMobileMenu();
            }
        });

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            initTheme();

            // Set initial expanded state for "Due today" section
            const dueTodayItem = document.querySelector('[data-toggle="due-today"]').closest('.nav-item');
            dueTodayItem.classList.add('expanded');
        });

        // Search Functionality (Basic)
        const searchInput = document.querySelector('.search-box input');
        searchInput.addEventListener('input', (event) => {
            const searchTerm = event.target.value.toLowerCase();
            const navItems = document.querySelectorAll('.nav-link span, .sub-nav-link');

            navItems.forEach(item => {
                const text = item.textContent.toLowerCase();
                const parentItem = item.closest('.nav-item, .sub-nav-item');

                if (text.includes(searchTerm) || searchTerm === '') {
                    parentItem.style.display = '';
                } else {
                    parentItem.style.display = 'none';
                }
            });
        });
    </script>
</body>
</html>
